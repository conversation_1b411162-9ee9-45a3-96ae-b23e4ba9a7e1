package com.dinglite.business.control;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.dinglite.common.constant.feign.*;
import com.dinglite.mongodb.api.constant.MongodbApiConstant;
import com.dinglite.mybatis.config.MapperScannerConstant;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;


/**
 * 控制应用程序
 *
 * <AUTHOR>
 * @date 2023/08/29
 */
@SpringBootApplication(exclude = DruidDataSourceAutoConfigure.class)
@MapperScan(MapperScannerConstant.CONTROL_SCAN)
@EnableAsync
@EnableFeignClients(basePackages = {
        RedisApiConstant.FEIGN_REDISSON_PACKAGE,
        UserApiConstant.USER_FEIGN_PACKAGE,
        ThirdApiConstant.FEIGN_PACKAGE,
        StoreApiConstant.FEIGN_PACKAGE,
        ProductApiConstant.FEIGN_PACKAGE,
        MongodbApiConstant.FEIGN_PACKAGE})
public class ControlApplication {
    public static void main(String[] args) {
        SpringApplication.run(ControlApplication.class, args);
    }

}
