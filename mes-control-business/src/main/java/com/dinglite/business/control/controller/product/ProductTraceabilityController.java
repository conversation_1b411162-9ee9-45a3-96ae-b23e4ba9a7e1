package com.dinglite.business.control.controller.product;

import com.dinglite.common.annotation.LoadPayload;
import com.dinglite.common.annotation.Log;
import com.dinglite.common.global.GlobalResult;
import com.dinglite.mongodb.api.domain.dto.ProductTraceabilityDTO;
import com.dinglite.mongodb.api.service.ProductTraceabilityClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 产品追溯控制器
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Api(tags = "产品追溯管理")
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/product/traceability")
public class ProductTraceabilityController {

    private final ProductTraceabilityClient productTraceabilityClient;

    /**
     * 通过SN码查询产品追溯信息
     *
     * @param sn 产品序列号
     * @return 产品追溯信息
     */
    @ApiOperation("通过SN码查询产品追溯信息")
    @PostMapping("/query-by-sn")
    @LoadPayload
    @Log(title = "产品追溯管理-查询")
    public GlobalResult<ProductTraceabilityDTO> queryProductTraceabilityBySn(
            @ApiParam(value = "产品序列号", required = true) @RequestParam String sn) {
        
        log.info("接收到产品追溯查询请求，SN: {}", sn);
        
        if (sn == null || sn.trim().isEmpty()) {
            return GlobalResult.failed("产品序列号不能为空");
        }

        try {
            ProductTraceabilityDTO result = productTraceabilityClient.getProductTraceabilityBySn(sn.trim());
            return GlobalResult.success(result);
        } catch (Exception e) {
            log.error("查询产品追溯信息失败，SN: {}", sn, e);
            return GlobalResult.failed("查询产品追溯信息失败: " + e.getMessage());
        }
    }
}
