<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mes-system</artifactId>
        <groupId>com.dinglite.mes</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>mes-control-business</artifactId>
    <name>business-mes系统管理模块</name>

    <dependencies>
        <dependency>
            <groupId>com.dinglite.mes</groupId>
            <artifactId>mes-basic-nacos</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.dinglite.mes</groupId>
            <artifactId>mes-basic-web</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.dinglite.mes</groupId>
            <artifactId>mes-basic-mybatis-plus</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.dinglite.mes</groupId>
            <artifactId>mes-module-user-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.dinglite.mes</groupId>
            <artifactId>mes-module-redis-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.dinglite.mes</groupId>
            <artifactId>mes-basic-rocketmq</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!-- SpringBoot Actuator 使得springboot admin监控显示信息更加详细-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- SpringBoot client 用于springboot admin管理-->
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dinglite.mes</groupId>
            <artifactId>mes-module-third-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.dinglite.mes</groupId>
            <artifactId>mes-module-store-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.dinglite.mes</groupId>
            <artifactId>mes-module-product-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.dinglite.mes</groupId>
            <artifactId>mes-module-mongodb-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>


        <!--集成logstash-->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>7.3</version>
        </dependency>


        <!--        &lt;!&ndash; https://mvnrepository.com/artifact/e-iceblue/spire.doc.free word报表模板&ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>e-iceblue</groupId>-->
        <!--            <artifactId>spire.doc.free</artifactId>-->
        <!--            <version>5.2.0</version>-->
        <!--            <scope>system</scope>-->
        <!--            <systemPath>${project.basedir}/src/main/resources/jar/spire.doc.free-5.2.0.jar</systemPath>-->
        <!--        </dependency>-->


        <!--        &lt;!&ndash; https://mvnrepository.com/artifact/com.google.zxing/core &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>com.google.zxing</groupId>-->
        <!--            <artifactId>core</artifactId>-->
        <!--            <version>3.3.0</version>-->
        <!--        </dependency>-->

        <!-- https://mvnrepository.com/artifact/org.dom4j/dom4j -->
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.4</version>
        </dependency>


        <!--        以下为获取服务器信息所用依赖-->
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
            <version>6.4.6</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.13.0</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna-platform</artifactId>
            <version>5.13.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>


    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>


</project>