# 产品追溯查询API文档

## 概述

产品追溯查询API提供了通过产品序列号（SN码）查询完整产品追溯信息的功能，包括：

1. 产品基本信息
2. 产品过站履历
3. 产品物料信息
4. 产品异常信息
5. 包装信息

## API接口

### 1. 通过SN码查询产品追溯信息

**接口地址：** `POST /mes-control-business/product/traceability/query-by-sn`

**请求方式：** POST

**请求参数：**

| 参数名 | 类型   | 必填 | 说明       |
|--------|--------|------|------------|
| sn     | String | 是   | 产品序列号 |

**请求示例：**

```http
POST /mes-control-business/product/traceability/query-by-sn
Content-Type: application/x-www-form-urlencoded

sn=SN202405200001
```

或者使用查询参数：

```http
POST /mes-control-business/product/traceability/query-by-sn?sn=SN202405200001
```

**响应格式：**

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "productInfo": {
      "id": "60f1b2c3d4e5f6789abcdef0",
      "collection": "product_info",
      "sn": "SN202405200001",
      "modelName": "产品型号名称",
      "modelCode": "MODEL001",
      "customerPN": "客户料号",
      "customerOrder": "客户订单号",
      "productionOrder": "生产工单号",
      "workshopName": "车间名称",
      "lineName": "产线名称",
      "lineCode": "LINE001",
      "status": "直通品",
      "isDirectProduct": true,
      "startTime": "2024-05-20 08:30:00",
      "lastProcess": "工序编码",
      "completedProcesses": ["GX01", "GX06"],
      "completedProcessNames": ["GX01(上料工序)", "GX06(喷码投料)"],
      "lastStation": "工站名称",
      "packageStatus": "已包装",
      "packageCode": "PKG001",
      "innerBoxCode": "INNER001",
      "outerBoxCode": "OUTER001",
      "palletCode": "PALLET001",
      "inboundTime": "2024-05-20 10:30:00",
      "outboundTime": "2024-05-20 12:30:00"
    },
    "productProcessList": [
      {
        "id": "60f1b2c3d4e5f6789abcdef1",
        "collection": "product_process",
        "processTime": "2024-05-20 08:42:25",
        "meta": {
          "lineCode": "LINE001",
          "operatorId": "OP001",
          "processCode": "PROC001",
          "sn": "SN202405200001"
        },
        "fixtureVersion": "V1.0",
        "result": "PASS",
        "parameters": {
          "voltage": 12.2,
          "temp": 35.6
        },
        "scanTime": "2024-05-20 08:30:00",
        "lineName": "产线名称",
        "stationIP": "**************",
        "stationName": "测试工站3号",
        "fixtureName": "FIX-2024-T01",
        "submitTime": "2024-05-20 08:31:05",
        "isRepairScan": false,
        "scanCode": "SCAN-0510-001",
        "failDesc": [],
        "failName": null,
        "failCode": null,
        "operatorName": "张三",
        "fixtureCode": "FIX-2024-T01",
        "processName": "功能测试",
        "isFirstProcess": true,
        "reworkProcessCode": null,
        "reworkProcessName": null
      }
    ],
    "productMaterialList": [
      {
        "id": "60f1b2c3d4e5f6789abcdef2",
        "collection": "product_material",
        "sn": "SN202405200001",
        "lineName": "16线",
        "lineCode": "LINE16",
        "productionOrder": "P2506135",
        "processName": "喷码投料",
        "processCode": "GX06",
        "materialName": "胶铁一体",
        "materialCode": "YALA0068-075V1",
        "materialSpec": "72.408 * 161.54 * 0.757",
        "supplier": "供应商名称",
        "unit": "卷",
        "bomNum": 1.0,
        "packageNum": 1100,
        "qrCode": "二维码内容",
        "boxNumber": "包装箱流水码",
        "lotNo": "202506",
        "productDate": "2025-06-09 00:00:00",
        "expirationDate": "2026-06-09 00:00:00",
        "stationIP": "************",
        "stationName": "16线喷码投料",
        "operatorId": "OP001",
        "operatorName": "操作员姓名",
        "submitTime": "2025-06-09 10:30:00",
        "startTime": "2025-06-09 10:00:00",
        "createTime": "2025-06-09 09:30:00",
        "bindIndex": 500
      }
    ],
    "productExceptionList": [
      {
        "id": "60f1b2c3d4e5f6789abcdef3",
        "collection": "product_exception",
        "sn": "SN202405200001",
        "modelCode": "MODEL001",
        "productionOrder": "生产工单号",
        "lineName": "产线名称",
        "lineCode": "LINE001",
        "status": "待复判",
        "isResolved": false,
        "processLog": ["PROC001", "PROC002"],
        "reportType": "1",
        "reportFail": "不良描述",
        "reportProcessCode": "PROC001",
        "reportProcessName": "工序名称",
        "reportStationIP": "*************",
        "reportStationName": "工站名称",
        "reportOperatorId": "OP001",
        "reportOperatorName": "张三",
        "reportTime": "2024-05-20 09:00:00"
      }
    ],
    "packageInfoList": [
      {
        "id": "60f1b2c3d4e5f6789abcdef4",
        "collection": "package_info",
        "packageType": "小包",
        "packageCode": "PKG001",
        "packageName": "小包装",
        "parentPackageCode": null,
        "capacity": 10,
        "currentCount": 8,
        "status": "已完成",
        "modelCode": "MODEL001",
        "modelName": "产品型号名称",
        "productionOrder": "生产工单号",
        "lineName": "产线名称",
        "lineCode": "LINE001",
        "stationName": "包装工站",
        "stationIP": "*************",
        "operatorId": "OP002",
        "operatorName": "李四",
        "startTime": "2024-05-20 09:30:00",
        "endTime": "2024-05-20 10:00:00",
        "productSnList": ["SN202405200001", "SN202405200002"],
        "childPackageCodes": [],
        "specification": "包装规格",
        "weight": 500.0,
        "dimensions": "100x50x30",
        "remark": "备注信息",
        "createTime": "2024-05-20 09:30:00",
        "updateTime": "2024-05-20 10:00:00"
      }
    ]
  }
}
```

## 错误响应

当查询失败时，返回错误信息：

```json
{
  "code": 500,
  "message": "查询产品追溯信息失败: 具体错误信息",
  "data": null
}
```

## 使用说明

1. **SN码格式**：请确保传入的SN码格式正确，系统会自动去除前后空格
2. **数据完整性**：返回的数据结构中，如果某个模块没有数据，对应的列表将为空数组
3. **工序名称映射**：
   - `completedProcesses`：包含原始的工序编码列表
   - `completedProcessNames`：包含格式化的工序名称列表，格式为"工序编码(工序名称)"
   - 如果找不到对应的工序名称，则只显示工序编码
4. **包装信息**：根据产品基本信息中的包装编码查询对应的包装详情

## 使用方式

1. **启动MongoDB服务**
2. **启动控制层服务（mes-control-business）**
3. **调用API接口**：
   ```http
   POST /mes-control-business/product/traceability/query-by-sn?sn=SN202405200001
   ```

## 注意事项

1. 该接口需要MongoDB服务正常运行
2. 确保传入的SN码在系统中存在
3. 接口支持跨服务调用，通过Feign客户端实现
4. 建议在生产环境中配置适当的超时时间和重试机制
5. **前端调用路径**：`POST /mes-control-business/product/traceability/query-by-sn`
