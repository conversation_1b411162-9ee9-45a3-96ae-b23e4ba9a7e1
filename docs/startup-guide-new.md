# MES系统启动指南-新

## 项目概览

- 旧指南是完全从零开始搭建环境，实际应该在数据库创建之后直接将生产环境的数据（包括表机构）直接复制
- 也可以数据库都不建，直接从数据库开始复制
- 本指南所有环境均为一台虚拟机搭建，至少分配16G内存，磁盘按需配置
- 本指南是通过ssh远程连接进入虚拟机开发，所以nacos的ip均可使用默认127.0.0.1成功连接，如果将环境搭建于服务器，本地运行需要修改很多ip配置，不仅有配置文件，还有NacosConstant.java中的DEFAULT_NACOS_ADDR也需要修改
- 特别注意，如果nacos的ip与mes-basic/mes-basic-nacos/src/main/java/com/dinglite/nacos/utils/NacosConstant.java中的DEFAULT_NACOS_ADDR不一致，会导致很多问题，比如服务注册失败，配置中心无法连接等，需要修改为实际的ip

### 系统架构
本MES系统是基于Spring Cloud微服务架构的制造执行系统，采用以下技术栈：

- **框架版本**: Spring Boot 2.1.6.RELEASE, Spring Cloud Greenwich.SR2
- **服务注册与配置**: Nacos 2.0.3
- **数据库**: MySQL 5.7, MongoDB 8.0.10
- **缓存**: Redis (密码: 123456)
- **分布式事务**: Seata
- **工作流引擎**: Flowable
- **API网关**: Spring Cloud Gateway
- **构建工具**: Maven
- **Java版本**: JDK 1.8

**注意**：如果觉得版本过老可使用docker运行相应服务，比如我的mysql、redis都是docker运行的

### 模块结构
```
mes-system/
├── mes-gateway/              # API网关模块 (端口: 8080)
├── mes-server/               # 服务器信息模块 (端口: 8050)
├── mes-control-business/     # 控制业务模块 (端口: 9002)
├── mes-modules/              # 业务模块集合
│   ├── mes-module-user/      # 用户服务 (端口: 8003)
│   ├── mes-module-redis/     # Redis服务 (端口: 8002)
│   ├── mes-module-third/     # 第三方服务 (端口: 8007)
│   ├── mes-module-store/     # 仓储服务 (端口: 8008)
│   ├── mes-module-product/   # 产品服务 (端口: 8009)
|   |── mes-module-mongodb/   # mongodb服务 (端口: 8010)
│   ├── mes-module-flowable/  # 工作流服务 (端口: 8011)(暂未使用)
│   └── mes-sso/              # 单点登录 (端口: 8004)
├── mes-monitor/              # 监控模块
├── mes-rocketmq/             # 消息队列模块
├── mes-basic/                # 基础组件模块
└── mes-common/               # 公共组件模块
```

## 环境要求

### 必需软件
1. **JDK 1.8**
2. **Maven 3.6+**
3. **MySQL 5.7+**
4. **Redis 6.0+**
5. **Nacos 2.0.3** (注意版本要求)
6. **MongoDB 8.0.10** (注意版本要求)

### 可选软件
1. **RocketMQ** (目前项目中未使用)
2. **HBase** (目前项目中未使用)

## 详细启动步骤

### 第一步：环境准备(默认java和maven已安装)

#### 1.1 安装和配置MySQL
```bash
# 安装MySQL 5.7+
# 连接MySQL并创建所需的数据库实例
mysql -u root -p

# 创建项目所需的数据库
CREATE DATABASE IF NOT EXISTS mes_control CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS mes_user CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS mes_store CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS mes_product CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS mes_third CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS nacos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS seata CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS mes_flowable CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;    # 可选，如果需要使用，还需要配置nacos中的连接参数

# 验证数据库创建成功
SHOW DATABASES;

# 退出MySQL
EXIT;
```

**重要配置**:
- 必须关闭`ONLY_FULL_GROUP_BY`模式
- 在MySQL配置文件(my.cnf或my.ini)中找到sql-mode配置项，删除`ONLY_FULL_GROUP_BY`
- 数据库连接参数示例：
  ```
  url: ***************************/{database_name}?serverTimezone=Asia/Shanghai&useSSL=false&useUnicode=true&characterEncoding=utf8
  username: root
  password: 123456
  ```

#### 1.2 安装和配置Redis
```bash
# 安装Redis 6.0+
# 设置Redis密码为: 123456
# 在redis.conf中配置:
requirepass 123456
```

#### 1.2.5 安装和配置MongoDB(不需要，数据采集存储到的mongodb，无需单独安装)

#### 1.3 安装和配置Nacos
```bash
# 下载Nacos 2.0.3 (注意版本要求)
wget https://github.com/alibaba/nacos/releases/download/2.0.3/nacos-server-2.0.3.tar.gz
tar -xzf nacos-server-2.0.3.tar.gz
cd nacos/bin

# 配置Nacos连接MySQL
# 编辑 conf/application.properties
spring.datasource.platform=mysql
db.num=1
db.url.0=***************************/nacos?characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useUnicode=true&useSSL=false&serverTimezone=UTC
db.user.0=root
db.password.0=123456

# 启动Nacos (standalone模式)
sh startup.sh -m standalone
```

**注意**：启动Nacos报错自行解决，可以问ai，比较简单

**Nacos配置说明**:
- 访问地址: http://localhost:8848/nacos
- 默认用户名/密码: nacos/nacos (可能是 nacos/dlt123456)
- 如果默认创建是nacos/nacos，导入生产环境数据是nacos/dlt123456

### 第二步：创建命名空间和配置数据源（数据导入后不用配置，当然也有可能有些连接配置项需要修改，自行查看按需修改）

**选择正确的命名空间**
- 切换到命名空间：`9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05`
- 这是项目Spring Boot应用实际使用的命名空间（由NacosConstant.java定义）
- **注意**：虽然registry.conf中配置的是`4d307a39-1034-4861-83fd-c6921c6d9c02`，但Spring Boot配置中心实际使用的是`9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05`

#### MongoDB连接配置(这个如果没有需要手动添加，mongodb模块是后面引入的)
##### 这个文件需要在Nacos配置中心创建，Data ID: mes-mongodb.properties，Group: DEFAULT_GROUP
```properties
# MongoDB基础连接配置
spring.data.mongodb.host=*************
spring.data.mongodb.port=27017
spring.data.mongodb.database=mes_mongodb
spring.data.mongodb.username=admin
spring.data.mongodb.password=123456
spring.data.mongodb.authentication-database=admin

# MongoDB连接池配置
spring.data.mongodb.option.connections-per-host=100
spring.data.mongodb.option.min-connections-per-host=10
spring.data.mongodb.option.max-wait-time=120000
spring.data.mongodb.option.max-connection-idle-time=0
spring.data.mongodb.option.max-connection-life-time=0
spring.data.mongodb.option.connect-timeout=10000
spring.data.mongodb.option.socket-timeout=0
spring.data.mongodb.option.heartbeat-frequency=10000
spring.data.mongodb.option.min-heartbeat-frequency=500

# MongoDB SSL配置（如果需要）
spring.data.mongodb.option.ssl-enabled=false
spring.data.mongodb.option.ssl-invalid-hostname-allowed=false

# MongoDB其他配置
spring.data.mongodb.option.always-use-m-beans=false
spring.data.mongodb.option.local-threshold=15
```

### 第三步：编译和安装项目

**重要**: 在启动任何服务之前，必须先构建整个项目并安装到本地Maven仓库

```bash
# 进入项目根目录
cd /home/<USER>/mes-system

# 清理并安装整个项目到本地仓库（这一步非常重要！）
mvn clean install -DskipTests

# 如果遇到依赖问题，可以强制更新
mvn clean install -DskipTests -U

# 验证构建结果
ls ~/.m2/repository/com/dinglite/mes/
```

**为什么需要 `mvn install`？**
- 本项目是多模块Maven项目，各模块间存在依赖关系
- `mvn install` 会将所有模块的JAR包安装到本地Maven仓库
- 只有安装后，其他模块才能找到并使用这些依赖

**常见构建错误排查**：
```bash
# 如果构建失败，可以尝试以下命令：

# 1. 清理所有target目录
find . -name "target" -type d -exec rm -rf {} + 2>/dev/null

# 2. 强制重新下载依赖
mvn dependency:purge-local-repository

# 3. 重新构建
mvn clean install -DskipTests -U
```

### 第四步：启动服务

**重要**: 必须按照以下顺序启动服务，确保依赖关系正确：

#### 4.1 启动基础服务
```bash
# 1. 启动Redis服务

# 2. 启动MySQL服务
# 确保MySQL服务正在运行

# 2.5. 启动MongoDB服务
# 确保MongoDB服务正在运行

# 3. 启动Nacos服务
cd ~/nacos/bin && sh startup.sh -m standalone
```

#### 4.2 启动业务服务

**注意**：也可以使用启动脚本（start-all.sh），区别在于手动启动可以有控制台信息进行debug，当然也可以修改启动脚本，将其日志输出到文件进行debug

**启动顺序很重要，请按照以下顺序启动：**

```bash
# 1. 启动Redis模块 (端口: 8002)
cd ~/mes-system/mes-modules/mes-module-redis/mes-module-redis-service
mvn spring-boot:run

# 2. 启动用户服务 (端口: 8003)
cd ~/mes-system/mes-modules/mes-module-user/mes-module-user-service
mvn spring-boot:run

# 3. 启动第三方服务 (端口: 8007)
cd ~/mes-system/mes-modules/mes-module-third/mes-module-third-service
mvn spring-boot:run

# 4. 启动仓储服务 (端口: 8008)
cd ~/mes-system/mes-modules/mes-module-store/mes-module-store-service
mvn spring-boot:run

# 5. 启动产品服务 (端口: 8009)
cd ~/mes-system/mes-modules/mes-module-product/mes-module-product-service
mvn spring-boot:run

# 5.5. 启动mongodb服务 (端口：8010)
cd ~/mes-system/mes-modules/mes-module-mongodb/mes-module-mongodb-service/
mvn spring-boot:run

# 6. 启动工作流服务 (端口: 8011)(不需要启动)
cd ~/mes-system/mes-modules/mes-module-flowable/mes-module-flowable-service
mvn spring-boot:run

# 7. 启动SSO服务 (端口: 8004)
cd ~/mes-system/mes-modules/mes-sso
mvn spring-boot:run

# 8. 启动控制业务模块 (端口: 9002)
cd ~/mes-system/mes-control-business
mvn spring-boot:run

# 9. 启动网关模块 (端口: 8080)
cd ~/mes-system/mes-gateway
mvn spring-boot:run

# 10. 启动服务器模块 (端口: 8050)
cd ~/mes-system/mes-server
mvn spring-boot:run
```

## 常见问题排查

### 问题1: Maven依赖解析错误
**错误信息**: `Could not resolve dependencies` 或 `Could not find artifact com.dinglite.mes:xxx:jar:1.0-SNAPSHOT`

**解决方案**:
```bash
# 1. 确保在项目根目录执行完整构建
cd /home/<USER>/mes-system
mvn clean install -DskipTests

# 2. 如果仍有问题，清理并重新构建
find . -name "target" -type d -exec rm -rf {} + 2>/dev/null
mvn clean install -DskipTests -U

# 3. 验证本地仓库中的依赖
ls ~/.m2/repository/com/dinglite/mes/

# 4. 如果特定模块有问题，可以单独构建
cd mes-basic
mvn clean install -DskipTests
cd ../mes-common
mvn clean install -DskipTests
```

**原因**: 多模块项目中，子模块依赖其他子模块的JAR包，必须先将所有模块安装到本地Maven仓库。

### 问题2: Nacos连接失败
**错误信息**: `no available service 'default' found, please make sure registry config correct`

**解决方案**:
1. 检查Nacos是否正常启动: `curl http://localhost:8848/nacos`
2. 确认Nacos配置文件中的命名空间ID正确
3. 检查各服务的`registry.conf`和`registry-dev.conf`文件中的Nacos地址
4. 确保Nacos用户名密码正确 (nacos/nacos 或 nacos/dlt123456)

### 问题3: Redis连接失败
**错误信息**: Redis连接被拒绝

**解决方案**:
1. 确保Redis服务正在运行
2. 检查Redis密码是否设置为`123456`
3. 确认Redis端口6379是否可访问

### 问题4: MySQL连接失败
**错误信息**: 数据库连接失败

**解决方案**:
1. 确保MySQL服务正在运行
2. 创建所需的数据库实例
3. 检查MySQL用户权限
4. 确认关闭了`ONLY_FULL_GROUP_BY`模式

### 问题5: 端口冲突
**解决方案**:
1. 检查端口占用: `netstat -tulpn | grep :端口号`
2. 修改对应服务的`bootstrap.properties`文件中的端口配置
3. 重新启动服务

### 问题6: Flowable数据库表不存在(不启动flowable服务即可)
**错误信息**: `Table 'control.ACT_GE_PROPERTY' doesn't exist` 或 `couldn't upgrade db schema`

**解决方案**:
1. 确保创建了flowable数据库：
```sql
CREATE DATABASE IF NOT EXISTS flowable CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 检查Nacos中的 `mes-datasource.properties` 是否包含flowable数据源配置：
```properties
spring.datasource.dynamic.datasource.flowable.url=***************************/flowable?serverTimezone=Asia/Shanghai&useSSL=false&useUnicode=true&characterEncoding=utf8
spring.datasource.dynamic.datasource.flowable.username=root
spring.datasource.dynamic.datasource.flowable.password=123456
spring.datasource.dynamic.datasource.flowable.driver-class-name=com.mysql.cj.jdbc.Driver
```

3. 验证数据库连接：
```bash
mysql -u root -p123456 -e "USE flowable; SHOW TABLES;"
```

**原因**: Flowable工作流引擎需要专用的数据库和表结构，配置了 `flowable.database-schema-update=true` 会自动创建表，但前提是数据源配置正确。

### 问题8: 服务启动顺序错误
**解决方案**:
1. 严格按照文档中的启动顺序执行
2. 等待前置服务完全启动后再启动下一个服务
3. 检查Nacos控制台确认服务注册成功

## 服务验证方法

### 验证Nacos
```bash
# 访问Nacos控制台
http://localhost:8848/nacos
# 用户名: nacos, 密码: nacos 或 dlt123456
```

### 验证Redis
```bash
# 连接Redis
redis-cli -a 123456
# 测试连接
ping
```

### 验证MySQL
```bash
# 连接MySQL
mysql -u root -p123456
# 查看数据库
show databases;
```

### 验证服务注册
在Nacos控制台的"服务管理" -> "服务列表"中应该能看到以下服务：
- mes-redis
- mes-user
- mes-third
- mes-store
- mes-product
<!-- - mes-flowable -->
- mes-sso
- mes-control-business
- mes-gateway
- mes-server

### 验证API网关
```bash
# 访问网关健康检查
curl http://localhost:8080/actuator/health
```

### 验证各个服务
```bash
# 检查服务健康状态
curl http://localhost:8002/actuator/health  # Redis服务
curl http://localhost:8003/actuator/health  # 用户服务
curl http://localhost:8007/actuator/health  # 第三方服务
curl http://localhost:8008/actuator/health  # 仓储服务
curl http://localhost:8009/actuator/health  # 产品服务
curl http://localhost:8010/actuator/health  # mongodb服务
curl http://localhost:8011/actuator/health  # 工作流服务
curl http://localhost:8004/actuator/health  # SSO服务
curl http://localhost:9002/actuator/health  # 控制业务
curl http://localhost:8080/actuator/health  # 网关
curl http://localhost:8050/actuator/health  # 服务器模块
```

## 重要注意事项

1. **Seata分布式事务**: 确保所有服务的事务组配置正确
2. **服务依赖**: 网关依赖用户服务和Redis服务，确保启动顺序正确
3. **日志级别**: 开发环境下设置了debug级别日志，生产环境建议调整