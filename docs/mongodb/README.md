# MES MongoDB模块

## 概述

MES MongoDB模块是MES系统中负责MongoDB数据库集成的微服务模块，提供MongoDB连接管理和基础数据模型定义。该模块采用轻量级设计，专注于基础功能，为后续业务扩展提供基础支撑。

## 功能特性

### 核心功能
- **MongoDB连接管理**: 提供MongoDB数据库连接配置和管理
- **健康检查**: 监控MongoDB连接状态和数据库信息
- **数据模型定义**: 定义生产相关的MongoDB实体类

### 技术特性
- **轻量级设计**: 简化的模块结构，专注于基础功能
- **健康监控**: 提供MongoDB连接状态检查和数据库信息查询
- **实体模型**: 完整的生产数据实体类定义
- **配置灵活**: 支持多种MongoDB连接配置选项

## 技术栈

- **Spring Boot**: 2.1.6.RELEASE
- **Spring Data MongoDB**: 2.1.9.RELEASE
- **MongoDB Java Driver**: 3.12.11
- **Spring Cloud**: Greenwich.SR2
- **Java**: 1.8

## 模块结构

```
mes-module-mongodb/
├── mes-module-mongodb-api/          # API接口模块
│   ├── constant/                    # 常量定义
│   └── domain/
│       └── entity/                  # MongoDB实体类
└── mes-module-mongodb-service/      # 服务实现模块
    ├── config/                      # MongoDB配置类
    ├── controller/                  # 健康检查控制器
    └── MongodbApplication.java      # 启动类
```

## 数据模型

### 1. ProductProcess (产品过站记录)
- 定义产品在生产线上的过站信息结构
- 包含测试结果、设备参数、操作员信息等字段
- 支持MongoDB文档映射和JSON序列化

### 2. ProductInfo (产品基础信息)
- 定义产品的基本信息和状态结构
- 包含型号、订单、产线、包装状态等字段
- 支持产品生命周期数据建模

### 3. ProductException (产品异常信息)
- 定义产品异常情况的数据结构
- 包含申报、复判、返修等环节字段
- 支持异常流程数据建模

### 4. ProductMaterial (产品物料追溯)
- 定义产品物料使用的数据结构
- 包含物料批次、供应商、绑定关系等字段
- 支持物料追溯数据建模

### 5. PackageInfo (包装信息)
- 定义产品包装相关的数据结构
- 支持多层级包装结构建模
- 包含包装容量和状态字段

## 配置说明

### MongoDB连接配置
```properties
# MongoDB基础配置
spring.data.mongodb.host=127.0.0.1
spring.data.mongodb.port=27017
spring.data.mongodb.database=mes_mongodb

# 连接池配置
spring.data.mongodb.option.connections-per-host=100
spring.data.mongodb.option.min-connections-per-host=10
spring.data.mongodb.option.max-wait-time=120000
spring.data.mongodb.option.connect-timeout=10000
```

### 应用配置
```properties
# 服务配置
server.port=8010
spring.application.name=mes-mongodb

# 日志配置
logging.level.com.dinglite=debug
```

## API接口

### 健康检查API
- `GET /mongodb/health/check` - 检查MongoDB连接状态
- `GET /mongodb/health/info` - 获取数据库信息
- `GET /mongodb/health/ping` - 测试MongoDB连接
- `GET /mongodb/health/version` - 获取MongoDB版本信息

## 使用示例

### 1. 检查MongoDB连接状态
```bash
curl http://localhost:8010/mongodb/health/check
```

### 2. 获取数据库信息
```bash
curl http://localhost:8010/mongodb/health/info
```

### 3. 使用实体类进行数据建模
```java
// 创建产品过站记录实体
ProductProcess process = new ProductProcess();
process.setResult("PASS");
process.setProcessTime(LocalDateTime.now());

// 设置元数据
ProductProcess.MetaData meta = new ProductProcess.MetaData();
meta.setSn("PRODUCT_001");
meta.setLineCode("LINE001");
process.setMeta(meta);
```

## 测试

### 编译模块
```bash
cd mes-modules/mes-module-mongodb
mvn clean compile
```

### 安装模块
```bash
mvn clean install -DskipTests
```

### 健康检查测试
启动服务后，可以通过以下方式测试MongoDB连接：
```bash
curl http://localhost:8010/mongodb/health/check
```

## 部署

### 1. 构建项目
```bash
mvn clean package
```

### 2. 启动服务
```bash
java -jar mes-module-mongodb-service/target/mes-mongodb.jar
```

### 3. Docker部署
```bash
docker build -t mes-mongodb .
docker run -p 8010:8010 mes-mongodb
```

## 监控

### 健康检查
- MongoDB模块应用健康状态: `http://localhost:8010/actuator/health`
- MongoDB连接状态: `http://localhost:8010/mongodb/health/check`
- MongoDB版本信息: `http://localhost:8010/mongodb/health/version`
- MongoDB数据库信息: `http://localhost:8010/mongodb/health/info`
- MongoDB连接测试: `http://localhost:8010/mongodb/health/ping`

## 注意事项

1. **数据库版本**: 确保MongoDB版本为8.0.10或兼容版本
2. **连接配置**: 根据实际环境配置MongoDB连接参数
3. **实体使用**: 实体类仅提供数据结构定义，具体业务逻辑需要自行实现
4. **扩展开发**: 可基于现有实体类扩展具体的业务功能
5. **健康监控**: 定期检查MongoDB连接状态和服务健康

## 故障排除

### 常见问题
1. **连接超时**: 检查MongoDB服务状态和网络连接
2. **查询慢**: 检查索引配置和查询条件
3. **内存不足**: 调整JVM参数和连接池配置
4. **数据不一致**: 检查事务配置和并发控制