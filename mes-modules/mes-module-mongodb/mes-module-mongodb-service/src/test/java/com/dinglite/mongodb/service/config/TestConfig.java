package com.dinglite.mongodb.service.config;

import com.dinglite.product.api.domain.dto.OperationDTO;
import com.dinglite.product.api.domain.query.OperationQuery;
import com.dinglite.product.api.service.OperationClient;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.util.Arrays;
import java.util.List;

/**
 * 测试配置类
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@TestConfiguration
public class TestConfig {

    /**
     * 创建Mock的OperationClient用于测试
     */
    @Bean
    @Primary
    public OperationClient mockOperationClient() {
        return new OperationClient() {
            @Override
            public OperationDTO create(OperationDTO operationDTO) {
                return null;
            }

            @Override
            public OperationDTO getInfo(Long operationId) {
                return null;
            }

            @Override
            public Boolean update(OperationDTO operationDTO) {
                return null;
            }

            @Override
            public Boolean delete(java.util.List<Long> operationIds) {
                return null;
            }

            @Override
            public com.dinglite.common.domain.PageDTO<OperationDTO> page(OperationQuery query) {
                return null;
            }

            @Override
            public List<OperationDTO> list(OperationQuery query) {
                // 返回模拟的工序数据，用于测试工序名称查询功能
                // 包含真实的工序编码GX06
                return Arrays.asList(
                    createMockOperation("GX01", "上料工序"),
                    createMockOperation("GX02", "检测工序"),
                    createMockOperation("GX03", "组装工序"),
                    createMockOperation("GX04", "测试工序"),
                    createMockOperation("GX05", "包装工序"),
                    createMockOperation("GX06", "喷码投料"),
                    createMockOperation("GX07", "质检工序"),
                    createMockOperation("GX08", "入库工序")
                );
            }

            private OperationDTO createMockOperation(String code, String name) {
                OperationDTO dto = new OperationDTO();
                dto.setOperationCode(code);
                dto.setOperationName(name);
                dto.setStatus(1); // 已提交状态
                return dto;
            }
        };
    }
}
