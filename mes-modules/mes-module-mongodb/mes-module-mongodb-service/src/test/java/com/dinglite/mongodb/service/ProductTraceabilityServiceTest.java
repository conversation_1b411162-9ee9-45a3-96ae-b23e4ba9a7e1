package com.dinglite.mongodb.service;

import com.dinglite.mongodb.api.domain.dto.ProductTraceabilityDTO;
import com.dinglite.mongodb.service.config.TestConfig;
import com.dinglite.mongodb.service.service.ProductTraceabilityService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

/**
 * 产品追溯服务测试类
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@SpringBootTest
@Import(TestConfig.class)
@ActiveProfiles("test")
public class ProductTraceabilityServiceTest {

    @Autowired
    private ProductTraceabilityService productTraceabilityService;

    @Test
    public void testGetProductTraceabilityBySn() {
        // 测试用的SN码，实际测试时需要使用真实的SN码
        String testSn = "B0675A015C633922A087401530";
        
        try {
            log.info("开始测试产品追溯查询，SN: {}", testSn);
            
            ProductTraceabilityDTO result = productTraceabilityService.getProductTraceabilityBySn(testSn);
            
            if (result != null) {
                log.info("查询成功，产品基本信息: {}", result.getProductInfo() != null ? "存在" : "不存在");
                log.info("工序信息数量: {}", result.getProductProcessList() != null ? result.getProductProcessList().size() : 0);
                log.info("物料信息数量: {}", result.getProductMaterialList() != null ? result.getProductMaterialList().size() : 0);
                log.info("异常信息数量: {}", result.getProductExceptionList() != null ? result.getProductExceptionList().size() : 0);
                log.info("包装信息数量: {}", result.getPackageInfoList() != null ? result.getPackageInfoList().size() : 0);

                // 测试工序名称查询功能
                if (result.getProductInfo() != null) {
                    log.info("已完成工序编码: {}", result.getProductInfo().getCompletedProcesses());
                    log.info("已完成工序名称: {}", result.getProductInfo().getCompletedProcessNames());

                    // 验证工序名称是否正确格式化
                    if (result.getProductInfo().getCompletedProcessNames() != null) {
                        for (String processName : result.getProductInfo().getCompletedProcessNames()) {
                            if (processName.contains("(") && processName.contains(")")) {
                                log.info("工序名称格式正确: {}", processName);
                            } else {
                                log.info("工序名称格式（仅编码）: {}", processName);
                            }
                        }
                    }
                }
            } else {
                log.warn("未查询到产品追溯信息，SN: {}", testSn);
            }
            
        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }
}
