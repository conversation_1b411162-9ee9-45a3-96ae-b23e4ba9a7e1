spring:
  data:
    mongodb:
      host: 127.0.0.1
      port: 27017
      database: mes_mongodb_test
      username: admin
      password: 123456
      authentication-database: admin
      option:
        connections-per-host: 50
        min-connections-per-host: 5
        max-wait-time: 30000
        connect-timeout: 10000
        socket-timeout: 30000
        server-selection-timeout: 30000
        max-connection-idle-time: 600000
        max-connection-life-time: 1800000

logging:
  level:
    com.dinglite: debug
    org.springframework.data.mongodb: debug
