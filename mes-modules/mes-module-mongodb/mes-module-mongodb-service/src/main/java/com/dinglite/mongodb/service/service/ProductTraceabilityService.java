package com.dinglite.mongodb.service.service;

import com.dinglite.mongodb.api.domain.dto.*;
import com.dinglite.mongodb.api.domain.entity.*;
import com.dinglite.product.api.domain.dto.OperationDTO;
import com.dinglite.product.api.domain.query.OperationQuery;
import com.dinglite.product.api.service.OperationClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品追溯服务类
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductTraceabilityService {

    private final MongoTemplate mongoTemplate;
    private final OperationClient operationClient;

    /**
     * 通过SN码查询完整的产品追溯信息
     * 
     * @param sn 产品序列号
     * @return 产品追溯信息
     */
    public ProductTraceabilityDTO getProductTraceabilityBySn(String sn) {
        log.info("开始查询产品追溯信息，SN: {}", sn);
        
        ProductTraceabilityDTO result = new ProductTraceabilityDTO();
        
        try {
            // 1. 查询产品基本信息
            ProductInfoDTO productInfo = getProductInfoBySn(sn);
            result.setProductInfo(productInfo);
            
            // 2. 查询产品过站履历
            List<ProductProcessDTO> productProcessList = getProductProcessListBySn(sn);
            result.setProductProcessList(productProcessList);
            
            // 3. 查询产品物料信息
            List<ProductMaterialDTO> productMaterialList = getProductMaterialListBySn(sn);
            result.setProductMaterialList(productMaterialList);
            
            // 4. 查询产品异常信息
            List<ProductExceptionDTO> productExceptionList = getProductExceptionListBySn(sn);
            result.setProductExceptionList(productExceptionList);
            
            // 5. 查询包装信息
            List<PackageInfoDTO> packageInfoList = getPackageInfoListBySn(sn, productInfo);
            result.setPackageInfoList(packageInfoList);
            
            log.info("产品追溯信息查询完成，SN: {}", sn);
            
        } catch (Exception e) {
            log.error("查询产品追溯信息异常，SN: {}", sn, e);
            throw new RuntimeException("查询产品追溯信息失败: " + e.getMessage(), e);
        }
        
        return result;
    }

    /**
     * 查询产品基本信息
     * 
     * @param sn 产品序列号
     * @return 产品基本信息
     */
    public ProductInfoDTO getProductInfoBySn(String sn) {
        try {
            Query query = new Query(Criteria.where("sn").is(sn));
            ProductInfo productInfo = mongoTemplate.findOne(query, ProductInfo.class, "product_info");
            
            if (productInfo == null) {
                log.warn("未找到产品基本信息，SN: {}", sn);
                return null;
            }
            
            ProductInfoDTO dto = new ProductInfoDTO();
            BeanUtils.copyProperties(productInfo, dto);
            
            // 处理已完成工序名称列表
            if (productInfo.getCompletedProcesses() != null && !productInfo.getCompletedProcesses().isEmpty()) {
                List<String> completedProcessNames = getProcessNamesByProcessCodes(productInfo.getCompletedProcesses());
                dto.setCompletedProcessNames(completedProcessNames);
            }
            
            return dto;
            
        } catch (Exception e) {
            log.error("查询产品基本信息异常，SN: {}", sn, e);
            throw new RuntimeException("查询产品基本信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询产品过站履历
     * 
     * @param sn 产品序列号
     * @return 产品过站履历列表
     */
    public List<ProductProcessDTO> getProductProcessListBySn(String sn) {
        try {
            Query query = new Query(Criteria.where("meta.sn").is(sn));
            List<ProductProcess> productProcessList = mongoTemplate.find(query, ProductProcess.class, "product_process");
            
            return productProcessList.stream().map(this::convertToProductProcessDTO).collect(Collectors.toList());
            
        } catch (Exception e) {
            log.error("查询产品过站履历异常，SN: {}", sn, e);
            throw new RuntimeException("查询产品过站履历失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询产品物料信息
     * 
     * @param sn 产品序列号
     * @return 产品物料信息列表
     */
    public List<ProductMaterialDTO> getProductMaterialListBySn(String sn) {
        try {
            Query query = new Query(Criteria.where("sn").is(sn));
            List<ProductMaterial> productMaterialList = mongoTemplate.find(query, ProductMaterial.class, "product_material");
            
            return productMaterialList.stream().map(this::convertToProductMaterialDTO).collect(Collectors.toList());
            
        } catch (Exception e) {
            log.error("查询产品物料信息异常，SN: {}", sn, e);
            throw new RuntimeException("查询产品物料信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询产品异常信息
     * 
     * @param sn 产品序列号
     * @return 产品异常信息列表
     */
    public List<ProductExceptionDTO> getProductExceptionListBySn(String sn) {
        try {
            Query query = new Query(Criteria.where("sn").is(sn));
            List<ProductException> productExceptionList = mongoTemplate.find(query, ProductException.class, "product_exception");
            
            return productExceptionList.stream().map(this::convertToProductExceptionDTO).collect(Collectors.toList());
            
        } catch (Exception e) {
            log.error("查询产品异常信息异常，SN: {}", sn, e);
            throw new RuntimeException("查询产品异常信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询包装信息
     *
     * @param sn 产品序列号
     * @param productInfo 产品基本信息
     * @return 包装信息列表
     */
    public List<PackageInfoDTO> getPackageInfoListBySn(String sn, ProductInfoDTO productInfo) {
        List<PackageInfoDTO> packageInfoList = new ArrayList<>();

        if (productInfo == null) {
            return packageInfoList;
        }

        try {
            // 从产品基本信息中获取包装编码
            List<String> packageCodes = new ArrayList<>();

            if (productInfo.getPackageCode() != null && !productInfo.getPackageCode().isEmpty()) {
                packageCodes.add(productInfo.getPackageCode());
            }
            if (productInfo.getInnerBoxCode() != null && !productInfo.getInnerBoxCode().isEmpty()) {
                packageCodes.add(productInfo.getInnerBoxCode());
            }
            if (productInfo.getOuterBoxCode() != null && !productInfo.getOuterBoxCode().isEmpty()) {
                packageCodes.add(productInfo.getOuterBoxCode());
            }
            if (productInfo.getPalletCode() != null && !productInfo.getPalletCode().isEmpty()) {
                packageCodes.add(productInfo.getPalletCode());
            }

            // 方法1：根据产品SN查询包装信息（查找包含该产品的包装）
            Query snQuery = new Query(Criteria.where("scannedCodes").in(sn));
            List<PackageInfo> packagesByProduct = mongoTemplate.find(snQuery, PackageInfo.class, "package_info");

            for (PackageInfo packageInfo : packagesByProduct) {
                PackageInfoDTO dto = convertToPackageInfoDTO(packageInfo);
                packageInfoList.add(dto);
                log.debug("通过产品SN查询到包装信息: serialNo={}, packageType={}",
                    packageInfo.getSerialNo(), packageInfo.getPackageType());
            }

            // 方法2：根据包装编码查询包装信息
            for (String packageCode : packageCodes) {
                Query query = new Query(Criteria.where("serialNo").is(packageCode));
                PackageInfo packageInfo = mongoTemplate.findOne(query, PackageInfo.class, "package_info");

                if (packageInfo != null) {
                    PackageInfoDTO dto = convertToPackageInfoDTO(packageInfo);
                    // 避免重复添加
                    boolean exists = packageInfoList.stream()
                        .anyMatch(existing -> existing.getSerialNo().equals(dto.getSerialNo()));
                    if (!exists) {
                        packageInfoList.add(dto);
                        log.debug("通过包装编码查询到包装信息: serialNo={}, packageType={}",
                            packageCode, packageInfo.getPackageType());
                    }
                } else {
                    log.warn("未找到包装信息: packageCode={}", packageCode);
                }
            }

        } catch (Exception e) {
            log.error("查询包装信息异常，SN: {}", sn, e);
            throw new RuntimeException("查询包装信息失败: " + e.getMessage(), e);
        }

        return packageInfoList;
    }

    /**
     * 根据工序编码获取工序名称
     * 参考产品信息追溯.cs中的SQL查询逻辑：
     * SELECT operation_code, operation_name FROM `operation` WHERE flag='N' ORDER BY operation_code
     *
     * @param processCodes 工序编码列表
     * @return 工序名称列表
     */
    private List<String> getProcessNamesByProcessCodes(List<String> processCodes) {
        if (processCodes == null || processCodes.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 查询所有有效的工序信息，构建编码到名称的映射
            OperationQuery query = new OperationQuery();
            List<OperationDTO> operationList = operationClient.list(query);

            // 构建工序编码到工序名称的映射字典
            Map<String, String> operationMap = operationList.stream()
                .collect(Collectors.toMap(
                    OperationDTO::getOperationCode,
                    OperationDTO::getOperationName,
                    (existing, replacement) -> existing // 如果有重复的key，保留第一个
                ));

            // 根据工序编码列表获取对应的工序名称
            List<String> processNames = new ArrayList<>();
            for (String processCode : processCodes) {
                if (operationMap.containsKey(processCode)) {
                    // 格式：工序编码(工序名称)，与C#代码保持一致
                    String processName = String.format("%s(%s)", processCode, operationMap.get(processCode));
                    processNames.add(processName);
                } else {
                    // 如果找不到对应的工序名称，只显示工序编码
                    processNames.add(processCode);
                    log.warn("未找到工序编码对应的工序名称: {}", processCode);
                }
            }

            return processNames;

        } catch (Exception e) {
            log.error("查询工序名称异常，工序编码列表: {}", processCodes, e);
            // 如果查询失败，返回原始的工序编码列表
            return processCodes;
        }
    }

    /**
     * 转换ProductProcess为ProductProcessDTO
     */
    private ProductProcessDTO convertToProductProcessDTO(ProductProcess entity) {
        ProductProcessDTO dto = new ProductProcessDTO();
        BeanUtils.copyProperties(entity, dto);
        
        if (entity.getMeta() != null) {
            ProductProcessDTO.MetaDataDTO metaDto = new ProductProcessDTO.MetaDataDTO();
            BeanUtils.copyProperties(entity.getMeta(), metaDto);
            dto.setMeta(metaDto);
        }
        
        return dto;
    }

    /**
     * 转换ProductMaterial为ProductMaterialDTO
     */
    private ProductMaterialDTO convertToProductMaterialDTO(ProductMaterial entity) {
        ProductMaterialDTO dto = new ProductMaterialDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 转换ProductException为ProductExceptionDTO
     */
    private ProductExceptionDTO convertToProductExceptionDTO(ProductException entity) {
        ProductExceptionDTO dto = new ProductExceptionDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 转换PackageInfo为PackageInfoDTO
     */
    private PackageInfoDTO convertToPackageInfoDTO(PackageInfo entity) {
        PackageInfoDTO dto = new PackageInfoDTO();
        BeanUtils.copyProperties(entity, dto);

        // 产品序列号列表已经通过BeanUtils.copyProperties复制了

        return dto;
    }
}
