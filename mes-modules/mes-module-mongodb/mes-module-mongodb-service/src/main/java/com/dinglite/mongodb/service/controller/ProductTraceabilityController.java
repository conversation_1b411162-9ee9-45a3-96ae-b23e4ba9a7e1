package com.dinglite.mongodb.service.controller;

import com.dinglite.mongodb.api.constant.MongodbApiConstant;
import com.dinglite.mongodb.api.domain.dto.ProductTraceabilityDTO;
import com.dinglite.mongodb.service.service.ProductTraceabilityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 产品追溯控制器
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Api(tags = "产品追溯管理")
@RestController
@RequestMapping(MongodbApiConstant.API_BASE_PATH + "/traceability")
@RequiredArgsConstructor
@Slf4j
public class ProductTraceabilityController {

    private final ProductTraceabilityService productTraceabilityService;

    @ApiOperation("通过SN码查询产品追溯信息")
    @GetMapping("/query")
    public ProductTraceabilityDTO getProductTraceabilityBySn(
            @ApiParam(value = "产品序列号", required = true) @RequestParam String sn) {
        
        log.info("接收到产品追溯查询请求，SN: {}", sn);
        
        if (sn == null || sn.trim().isEmpty()) {
            throw new IllegalArgumentException("产品序列号不能为空");
        }
        
        return productTraceabilityService.getProductTraceabilityBySn(sn.trim());
    }
}
