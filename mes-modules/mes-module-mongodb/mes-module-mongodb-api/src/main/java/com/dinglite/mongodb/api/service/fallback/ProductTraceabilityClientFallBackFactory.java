package com.dinglite.mongodb.api.service.fallback;

import com.dinglite.mongodb.api.domain.dto.ProductTraceabilityDTO;
import com.dinglite.mongodb.api.service.ProductTraceabilityClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 产品追溯Feign客户端降级工厂
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@Component
public class ProductTraceabilityClientFallBackFactory implements FallbackFactory<ProductTraceabilityClient> {

    @Override
    public ProductTraceabilityClient create(Throwable cause) {
        return new ProductTraceabilityClient() {
            @Override
            public ProductTraceabilityDTO getProductTraceabilityBySn(String sn) {
                log.error("调用产品追溯服务失败，SN: {}", sn, cause);
                throw new RuntimeException("产品追溯服务暂时不可用，请稍后重试");
            }
        };
    }
}
