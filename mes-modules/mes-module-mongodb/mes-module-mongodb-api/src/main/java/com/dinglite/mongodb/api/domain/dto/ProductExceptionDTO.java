package com.dinglite.mongodb.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品异常信息DTO
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProductExceptionDTO对象", description = "产品异常信息")
public class ProductExceptionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "MongoDB 文档主键")
    @JsonProperty("_id")
    private String id;

    @ApiModelProperty(value = "集合名称")
    @JsonProperty("collection")
    private String collection;

    @ApiModelProperty(value = "产品序列号")
    @JsonProperty("sn")
    private String sn;

    @ApiModelProperty(value = "产品型号编码")
    @JsonProperty("modelCode")
    private String modelCode;

    @ApiModelProperty(value = "生产工单号")
    @JsonProperty("productionOrder")
    private String productionOrder;

    @ApiModelProperty(value = "产线名称")
    @JsonProperty("lineName")
    private String lineName;

    @ApiModelProperty(value = "产线编码")
    @JsonProperty("lineCode")
    private String lineCode;

    @ApiModelProperty(value = "产品状态")
    @JsonProperty("status")
    private String status;

    @ApiModelProperty(value = "不良流程是否已完成")
    @JsonProperty("isResolved")
    private Boolean isResolved;

    @ApiModelProperty(value = "已完成工序列表")
    @JsonProperty("processLog")
    private List<String> processLog;

    // 申报信息
    @ApiModelProperty(value = "申报判定类型")
    @JsonProperty("reportType")
    private String reportType;

    @ApiModelProperty(value = "申报不良描述")
    @JsonProperty("reportFail")
    private String reportFail;

    @ApiModelProperty(value = "申报不良工序编码")
    @JsonProperty("reportProcessCode")
    private String reportProcessCode;

    @ApiModelProperty(value = "申报工序名称")
    @JsonProperty("reportProcessName")
    private String reportProcessName;

    @ApiModelProperty(value = "申报工站IP地址")
    @JsonProperty("reportStationIP")
    private String reportStationIP;

    @ApiModelProperty(value = "申报工站名称")
    @JsonProperty("reportStationName")
    private String reportStationName;

    @ApiModelProperty(value = "申报操作员账号")
    @JsonProperty("reportOperatorId")
    private String reportOperatorId;

    @ApiModelProperty(value = "申报操作员姓名")
    @JsonProperty("reportOperatorName")
    private String reportOperatorName;

    @ApiModelProperty(value = "不良申报时间")
    @JsonProperty("reportTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportTime;

    // 复判信息
    @ApiModelProperty(value = "复判判定类型")
    @JsonProperty("judgeType")
    private String judgeType;

    @ApiModelProperty(value = "复判不良描述")
    @JsonProperty("judgeFail")
    private String judgeFail;

    @ApiModelProperty(value = "复判不良工序编码")
    @JsonProperty("judgeProcessCode")
    private String judgeProcessCode;

    @ApiModelProperty(value = "复判工序名称")
    @JsonProperty("judgeProcessName")
    private String judgeProcessName;

    @ApiModelProperty(value = "复判工站IP地址")
    @JsonProperty("judgeStationIP")
    private String judgeStationIP;

    @ApiModelProperty(value = "复判工站名称")
    @JsonProperty("judgeStationName")
    private String judgeStationName;

    @ApiModelProperty(value = "复判操作员账号")
    @JsonProperty("judgeOperatorId")
    private String judgeOperatorId;

    @ApiModelProperty(value = "复判操作员姓名")
    @JsonProperty("judgeOperatorName")
    private String judgeOperatorName;

    @ApiModelProperty(value = "复判时间")
    @JsonProperty("judgeTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime judgeTime;

    // 返修信息
    @ApiModelProperty(value = "返修判定类型")
    @JsonProperty("repairType")
    private String repairType;

    @ApiModelProperty(value = "返修不良描述")
    @JsonProperty("repairFail")
    private String repairFail;

    @ApiModelProperty(value = "返修不良工序编码")
    @JsonProperty("repairProcessCode")
    private String repairProcessCode;

    @ApiModelProperty(value = "返修工序名称")
    @JsonProperty("repairProcessName")
    private String repairProcessName;

    @ApiModelProperty(value = "返修工站IP地址")
    @JsonProperty("repairStationIP")
    private String repairStationIP;

    @ApiModelProperty(value = "返修工站名称")
    @JsonProperty("repairStationName")
    private String repairStationName;

    @ApiModelProperty(value = "返修操作员账号")
    @JsonProperty("repairOperatorId")
    private String repairOperatorId;

    @ApiModelProperty(value = "返修操作员姓名")
    @JsonProperty("repairOperatorName")
    private String repairOperatorName;

    @ApiModelProperty(value = "返修时间")
    @JsonProperty("repairTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime repairTime;

    @ApiModelProperty(value = "重投工序编码")
    @JsonProperty("reinputProcessCode")
    private String reinputProcessCode;

    @ApiModelProperty(value = "重投工序名称")
    @JsonProperty("reinputProcessName")
    private String reinputProcessName;
}
