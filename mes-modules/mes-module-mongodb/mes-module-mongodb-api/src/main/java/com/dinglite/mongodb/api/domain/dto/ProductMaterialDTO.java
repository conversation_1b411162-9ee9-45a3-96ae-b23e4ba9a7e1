package com.dinglite.mongodb.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 产品物料追溯信息DTO
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProductMaterialDTO对象", description = "产品物料追溯信息")
public class ProductMaterialDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "MongoDB 文档主键")
    @JsonProperty("_id")
    private String id;

    @ApiModelProperty(value = "目标集合名称")
    @JsonProperty("collection")
    private String collection;

    @ApiModelProperty(value = "产品序列号")
    @JsonProperty("sn")
    private String sn;

    @ApiModelProperty(value = "产线名称")
    @JsonProperty("lineName")
    private String lineName;

    @ApiModelProperty(value = "产线编码")
    @JsonProperty("lineCode")
    private String lineCode;

    @ApiModelProperty(value = "生产工单号")
    @JsonProperty("productionOrder")
    private String productionOrder;

    @ApiModelProperty(value = "用料工序名称")
    @JsonProperty("processName")
    private String processName;

    @ApiModelProperty(value = "用料工序编码")
    @JsonProperty("processCode")
    private String processCode;

    @ApiModelProperty(value = "物料名称")
    @JsonProperty("materialName")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    @JsonProperty("materialCode")
    private String materialCode;

    @ApiModelProperty(value = "物料规格")
    @JsonProperty("materialSpec")
    private String materialSpec;

    @ApiModelProperty(value = "供应商名称")
    @JsonProperty("supplier")
    private String supplier;

    @ApiModelProperty(value = "计量单位")
    @JsonProperty("unit")
    private String unit;

    @ApiModelProperty(value = "BOM 标准用量")
    @JsonProperty("bomNum")
    private Double bomNum;

    @ApiModelProperty(value = "单包装数量")
    @JsonProperty("packageNum")
    private Integer packageNum;

    @ApiModelProperty(value = "物料二维码内容")
    @JsonProperty("qrCode")
    private String qrCode;

    @ApiModelProperty(value = "包装箱流水码")
    @JsonProperty("boxNumber")
    private String boxNumber;

    @ApiModelProperty(value = "生产批次号")
    @JsonProperty("lotNo")
    private String lotNo;

    @ApiModelProperty(value = "物料生产日期")
    @JsonProperty("productDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime productDate;

    @ApiModelProperty(value = "物料有效期")
    @JsonProperty("expirationDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expirationDate;

    @ApiModelProperty(value = "上料工站 IP 地址")
    @JsonProperty("stationIP")
    private String stationIP;

    @ApiModelProperty(value = "上料工站名称")
    @JsonProperty("stationName")
    private String stationName;

    @ApiModelProperty(value = "操作员系统账号")
    @JsonProperty("operatorId")
    private String operatorId;

    @ApiModelProperty(value = "操作员姓名")
    @JsonProperty("operatorName")
    private String operatorName;

    @ApiModelProperty(value = "数据提交时间")
    @JsonProperty("submitTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    @ApiModelProperty(value = "物料启用时间")
    @JsonProperty("startTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "物料绑定时间")
    @JsonProperty("createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "物料绑定序号")
    @JsonProperty("bindIndex")
    private Integer bindIndex;
}
