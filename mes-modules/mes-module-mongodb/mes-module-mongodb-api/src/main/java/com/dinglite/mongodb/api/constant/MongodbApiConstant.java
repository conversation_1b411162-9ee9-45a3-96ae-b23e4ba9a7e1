package com.dinglite.mongodb.api.constant;

/**
 * MongoDB模块API常量
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
public class MongodbApiConstant {

    /**
     * Feign服务名称
     */
    public static final String FEIGN_NAME = "mes-mongodb";

    /**
     * Feign客户端包路径
     */
    public static final String FEIGN_PACKAGE = "com.dinglite.mongodb.api.service";

    /**
     * API基础路径
     */
    public static final String API_BASE_PATH = "/mongodb";

    /**
     * 健康检查API路径
     */
    public static final String HEALTH_API_PATH = API_BASE_PATH + "/health";

    /**
     * MongoDB集合名称常量
     */
    public static class Collections {
        public static final String PRODUCT_PROCESS = "product_process";
        public static final String PRODUCT_INFO = "product_info";
        public static final String PRODUCT_EXCEPTION = "product_exception";
        public static final String PRODUCT_MATERIAL = "product_material";
        public static final String PACKAGE_INFO = "package_info";
    }
}
