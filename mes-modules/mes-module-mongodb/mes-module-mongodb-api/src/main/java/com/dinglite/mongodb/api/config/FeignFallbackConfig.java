package com.dinglite.mongodb.api.config;

import com.dinglite.mongodb.api.service.fallback.ProductTraceabilityClientFallback;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Feign降级配置类
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Configuration
public class FeignFallbackConfig {

    /**
     * 注册产品追溯客户端降级Bean
     */
    @Bean
    public ProductTraceabilityClientFallback productTraceabilityClientFallback() {
        return new ProductTraceabilityClientFallback();
    }
}
