package com.dinglite.mongodb.api.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 产品追溯信息DTO
 * 用于封装完整的产品追溯信息，包括基本信息、工序信息、物料信息、异常信息和包装信息
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProductTraceabilityDTO对象", description = "产品追溯信息")
public class ProductTraceabilityDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品基本信息")
    private ProductInfoDTO productInfo;

    @ApiModelProperty(value = "产品工序信息列表")
    private List<ProductProcessDTO> productProcessList;

    @ApiModelProperty(value = "产品物料信息列表")
    private List<ProductMaterialDTO> productMaterialList;

    @ApiModelProperty(value = "产品异常信息列表")
    private List<ProductExceptionDTO> productExceptionList;

    @ApiModelProperty(value = "包装信息列表")
    private List<PackageInfoDTO> packageInfoList;
}
