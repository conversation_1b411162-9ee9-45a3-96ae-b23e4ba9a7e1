package com.dinglite.mongodb.api.domain.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.ArrayList;
import java.util.List;

/**
 * 关联包装层级关系
 * 描述当前包装包含的下级包装序列号
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@ApiModel(value = "RelatedPackages对象", description = "关联包装层级关系")
public class RelatedPackages {

    @ApiModelProperty(value = "小包编码列表")
    @Field("small")
    @JsonProperty("small")
    private List<String> small = new ArrayList<>();

    @ApiModelProperty(value = "内箱编码列表")
    @Field("inner")
    @JsonProperty("inner")
    private List<String> inner = new ArrayList<>();

    @ApiModelProperty(value = "外箱编码列表")
    @Field("outer")
    @JsonProperty("outer")
    private List<String> outer = new ArrayList<>();

    @ApiModelProperty(value = "栈板编码列表")
    @Field("pallet")
    @JsonProperty("pallet")
    private List<String> pallet = new ArrayList<>();
}
