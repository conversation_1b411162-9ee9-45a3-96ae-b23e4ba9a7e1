package com.dinglite.mongodb.api.service;

import com.dinglite.mongodb.api.constant.MongodbApiConstant;
import com.dinglite.mongodb.api.domain.dto.ProductTraceabilityDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 产品追溯Feign客户端
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@FeignClient(
    name = MongodbApiConstant.FEIGN_NAME,
    contextId = "productTraceabilityClient"
)
@RequestMapping(MongodbApiConstant.API_BASE_PATH + "/traceability")
public interface ProductTraceabilityClient {

    /**
     * 通过SN码查询产品追溯信息
     * 
     * @param sn 产品序列号
     * @return 产品追溯信息
     */
    @GetMapping("/query")
    ProductTraceabilityDTO getProductTraceabilityBySn(@RequestParam("sn") String sn);
}
