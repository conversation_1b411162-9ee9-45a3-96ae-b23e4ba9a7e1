package com.dinglite.mongodb.api.domain.dto;

import com.dinglite.mongodb.api.domain.entity.RelatedPackages;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 包装信息DTO
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PackageInfoDTO对象", description = "包装信息")
public class PackageInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "MongoDB自动生成的唯一标识符")
    @JsonProperty("_id")
    private String id;

    @ApiModelProperty(value = "目标集合名称")
    @JsonProperty("collection")
    private String collection;

    @ApiModelProperty(value = "包装层级类型")
    @JsonProperty("packageType")
    private String packageType;

    @ApiModelProperty(value = "包装唯一序列号")
    @JsonProperty("serialNo")
    private String serialNo;

    @ApiModelProperty(value = "包装二维码全文本")
    @JsonProperty("qrCode")
    private String qrCode;



    @ApiModelProperty(value = "包装状态")
    @JsonProperty("status")
    private String status;

    @ApiModelProperty(value = "产品型号编码")
    @JsonProperty("modelCode")
    private String modelCode;



    @ApiModelProperty(value = "生产工单号")
    @JsonProperty("productionOrder")
    private String productionOrder;

    @ApiModelProperty(value = "客户料号")
    @JsonProperty("customerPN")
    private String customerPN;

    @ApiModelProperty(value = "客户订单号")
    @JsonProperty("customerOrder")
    private String customerOrder;

    @ApiModelProperty(value = "物料包装数量")
    @JsonProperty("packageNum")
    private Integer packageNum;

    @ApiModelProperty(value = "批次号")
    @JsonProperty("lotNo")
    private String lotNo;

    @ApiModelProperty(value = "生产日期")
    @JsonProperty("productionDate")
    private LocalDateTime productionDate;

    @ApiModelProperty(value = "有效截止日期")
    @JsonProperty("expirationDate")
    private LocalDateTime expirationDate;

    @ApiModelProperty(value = "产线名称")
    @JsonProperty("lineName")
    private String lineName;

    @ApiModelProperty(value = "产线编码")
    @JsonProperty("lineCode")
    private String lineCode;

    @ApiModelProperty(value = "包装工站名称")
    @JsonProperty("stationName")
    private String stationName;

    @ApiModelProperty(value = "包装工站IP")
    @JsonProperty("stationIP")
    private String stationIP;

    @ApiModelProperty(value = "操作员账号")
    @JsonProperty("operatorId")
    private String operatorId;

    @ApiModelProperty(value = "操作员姓名")
    @JsonProperty("operatorName")
    private String operatorName;

    @ApiModelProperty(value = "包装开始时间")
    @JsonProperty("startTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "包装完成时间")
    @JsonProperty("endTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "入库时间")
    @JsonProperty("inboundTime")
    private LocalDateTime inboundTime;

    @ApiModelProperty(value = "出库时间")
    @JsonProperty("outboundTime")
    private LocalDateTime outboundTime;

    @ApiModelProperty(value = "扫描码集合")
    @JsonProperty("scannedCodes")
    private List<String> scannedCodes = new ArrayList<>();

    @ApiModelProperty(value = "关联包装层级关系")
    @JsonProperty("relatedPackages")
    private RelatedPackages relatedPackages;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty("createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;



    /**
     * 关联包装层级关系DTO
     */
    @Data
    @ApiModel(value = "RelatedPackagesDTO对象", description = "关联包装层级关系")
    public static class RelatedPackagesDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "小包编码列表")
        @JsonProperty("small")
        private List<String> small;

        @ApiModelProperty(value = "内箱编码列表")
        @JsonProperty("inner")
        private List<String> inner;

        @ApiModelProperty(value = "外箱编码列表")
        @JsonProperty("outer")
        private List<String> outer;

        @ApiModelProperty(value = "栈板编码")
        @JsonProperty("pallet")
        private String pallet;
    }
}
