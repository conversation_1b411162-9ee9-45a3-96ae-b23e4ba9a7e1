package com.dinglite.mongodb.api.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 产品基础信息实体类（MongoDB 存储对象）
 * 映射到 MongoDB 集合 product_info
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProductInfo对象", description = "产品基础信息")
@Document(collection = "product_info")
public class ProductInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "MongoDB 文档唯一标识（ObjectId）")
    @Id
    @JsonProperty("_id")
    private String id;

    @ApiModelProperty(value = "集合名称")
    @Field("collection")
    @JsonProperty("collection")
    private String collection;

    @ApiModelProperty(value = "产品序列号（业务主键）")
    @Field("sn")
    @JsonProperty("sn")
    private String sn;

    @ApiModelProperty(value = "产品型号名称")
    @Field("modelName")
    @JsonProperty("modelName")
    private String modelName;

    @ApiModelProperty(value = "产品型号编码")
    @Field("modelCode")
    @JsonProperty("modelCode")
    private String modelCode;

    @ApiModelProperty(value = "客户物料号")
    @Field("customerPN")
    @JsonProperty("customerPN")
    private String customerPN;

    @ApiModelProperty(value = "客户订单号")
    @Field("customerOrder")
    @JsonProperty("customerOrder")
    private String customerOrder;

    @ApiModelProperty(value = "生产工单号")
    @Field("productionOrder")
    @JsonProperty("productionOrder")
    private String productionOrder;

    @ApiModelProperty(value = "生产车间名称")
    @Field("workshopName")
    @JsonProperty("workshopName")
    private String workshopName;

    @ApiModelProperty(value = "产线名称")
    @Field("lineName")
    @JsonProperty("lineName")
    private String lineName;

    @ApiModelProperty(value = "产线编码")
    @Field("lineCode")
    @JsonProperty("lineCode")
    private String lineCode;

    @ApiModelProperty(value = "产品状态")
    @Field("status")
    @JsonProperty("status")
    private String status;

    @ApiModelProperty(value = "是否直通品标识")
    @Field("isDirectProduct")
    @JsonProperty("isDirectProduct")
    private Boolean isDirectProduct;

    @ApiModelProperty(value = "投产时间（UTC 时间）")
    @Field("startTime")
    @JsonProperty("startTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "最后工序名称")
    @Field("lastProcess")
    @JsonProperty("lastProcess")
    private String lastProcess;

    @ApiModelProperty(value = "已完成工序列表")
    @Field("completedProcesses")
    @JsonProperty("completedProcesses")
    private List<String> completedProcesses = new ArrayList<>();

    @ApiModelProperty(value = "最后工站名称")
    @Field("lastStation")
    @JsonProperty("lastStation")
    private String lastStation;

    @ApiModelProperty(value = "包装状态")
    @Field("packageStatus")
    @JsonProperty("packageStatus")
    private String packageStatus;

    @ApiModelProperty(value = "小包装编码")
    @Field("packageCode")
    @JsonProperty("packageCode")
    private String packageCode;

    @ApiModelProperty(value = "内箱编码")
    @Field("innerBoxCode")
    @JsonProperty("innerBoxCode")
    private String innerBoxCode;

    @ApiModelProperty(value = "外箱编码")
    @Field("outerBoxCode")
    @JsonProperty("outerBoxCode")
    private String outerBoxCode;

    @ApiModelProperty(value = "栈板编码")
    @Field("palletCode")
    @JsonProperty("palletCode")
    private String palletCode;

    @ApiModelProperty(value = "入库时间（UTC 时间，可为空）")
    @Field("inboundTime")
    @JsonProperty("inboundTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inboundTime;

    @ApiModelProperty(value = "出库时间（UTC 时间，可为空）")
    @Field("outboundTime")
    @JsonProperty("outboundTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime outboundTime;

    /**
     * 产品状态枚举
     */
    public enum ProductStatus {
        直通品(0, "直通品"),
        待复判(1, "待复判"),
        复判良品(2, "复判良品"),
        复判返修(3, "复判返修"),
        复判报废(4, "复判报废"),
        返修中(5, "返修中"),
        返修良品(6, "返修良品"),
        返修报废(7, "返修报废");

        private final int code;
        private final String description;

        ProductStatus(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 产品包装状态枚举
     */
    public enum PackageStatus {
        未包装(0, "未包装"),
        包小包中(1, "打小包中"),
        已包小包(2, "已入小包"),
        包内箱中(3, "包内箱中"),
        已包内箱(4, "已入内箱"),
        包外箱中(5, "包外箱中"),
        已包外箱(6, "已入外箱"),
        垒托中(7, "垒托中"),
        已垒托(8, "已垒托"),
        入库中(9, "入库中"),
        已入库(10, "已入库"),
        出库中(11, "出库中"),
        已出库(12, "已出库");

        private final int code;
        private final String description;

        PackageStatus(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
