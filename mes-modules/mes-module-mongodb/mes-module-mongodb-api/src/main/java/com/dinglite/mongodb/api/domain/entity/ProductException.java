package com.dinglite.mongodb.api.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 产品异常信息实体类（MongoDB 存储对象）
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProductException对象", description = "产品异常信息")
@Document(collection = "product_exception")
public class ProductException implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "MongoDB 文档主键（ObjectId 类型，自动生成）")
    @Id
    @JsonProperty("_id")
    private String id;

    @ApiModelProperty(value = "集合名称")
    @Field("collection")
    @JsonProperty("collection")
    private String collection = "product_exception";

    @ApiModelProperty(value = "产品序列号（唯一标识）")
    @Field("sn")
    @JsonProperty("sn")
    private String sn;

    @ApiModelProperty(value = "产品型号编码")
    @Field("modelCode")
    @JsonProperty("modelCode")
    private String modelCode;

    @ApiModelProperty(value = "生产工单号")
    @Field("productionOrder")
    @JsonProperty("productionOrder")
    private String productionOrder;

    @ApiModelProperty(value = "产线名称")
    @Field("lineName")
    @JsonProperty("lineName")
    private String lineName;

    @ApiModelProperty(value = "产线编码")
    @Field("lineCode")
    @JsonProperty("lineCode")
    private String lineCode;

    @ApiModelProperty(value = "产品状态")
    @Field("status")
    @JsonProperty("status")
    private String status = "";

    @ApiModelProperty(value = "不良流程是否已完成")
    @Field("isResolved")
    @JsonProperty("isResolved")
    private Boolean isResolved = false;

    @ApiModelProperty(value = "已完成工序列表")
    @Field("processLog")
    @JsonProperty("processLog")
    private List<String> processLog = new ArrayList<>();

    // 申报信息
    @ApiModelProperty(value = "判定类型（0：良品，1：待复判， 2：返修， 3：报废）")
    @Field("reportType")
    @JsonProperty("reportType")
    private String reportType;

    @ApiModelProperty(value = "不良描述")
    @Field("reportFail")
    @JsonProperty("reportFail")
    private String reportFail;

    @ApiModelProperty(value = "申报不良工序编码")
    @Field("reportProcessCode")
    @JsonProperty("reportProcessCode")
    private String reportProcessCode;

    @ApiModelProperty(value = "工序名称")
    @Field("reportProcessName")
    @JsonProperty("reportProcessName")
    private String reportProcessName;

    @ApiModelProperty(value = "工站IP地址")
    @Field("reportStationIP")
    @JsonProperty("reportStationIP")
    private String reportStationIP;

    @ApiModelProperty(value = "工站名称")
    @Field("reportStationName")
    @JsonProperty("reportStationName")
    private String reportStationName;

    @ApiModelProperty(value = "操作员账号")
    @Field("reportOperatorId")
    @JsonProperty("reportOperatorId")
    private String reportOperatorId;

    @ApiModelProperty(value = "操作员姓名")
    @Field("reportOperatorName")
    @JsonProperty("reportOperatorName")
    private String reportOperatorName;

    @ApiModelProperty(value = "不良申报时间")
    @Field("reportTime")
    @JsonProperty("reportTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportTime;

    // 复判信息
    @ApiModelProperty(value = "判定类型（0：良品，1：待复判， 2：返修， 3：报废）")
    @Field("judgeType")
    @JsonProperty("judgeType")
    private String judgeType;

    @ApiModelProperty(value = "不良描述")
    @Field("judgeFail")
    @JsonProperty("judgeFail")
    private String judgeFail;

    @ApiModelProperty(value = "申报不良工序编码")
    @Field("judgeProcessCode")
    @JsonProperty("judgeProcessCode")
    private String judgeProcessCode;

    @ApiModelProperty(value = "工序名称")
    @Field("judgeProcessName")
    @JsonProperty("judgeProcessName")
    private String judgeProcessName;

    @ApiModelProperty(value = "工站IP地址")
    @Field("judgeStationIP")
    @JsonProperty("judgeStationIP")
    private String judgeStationIP;

    @ApiModelProperty(value = "工站名称")
    @Field("judgeStationName")
    @JsonProperty("judgeStationName")
    private String judgeStationName;

    @ApiModelProperty(value = "操作员账号")
    @Field("judgeOperatorId")
    @JsonProperty("judgeOperatorId")
    private String judgeOperatorId;

    @ApiModelProperty(value = "操作员姓名")
    @Field("judgeOperatorName")
    @JsonProperty("judgeOperatorName")
    private String judgeOperatorName;

    @ApiModelProperty(value = "不良申报时间")
    @Field("judgeTime")
    @JsonProperty("judgeTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime judgeTime;

    // 返修信息
    @ApiModelProperty(value = "判定类型（0：良品，1：待复判， 2：返修， 3：报废）")
    @Field("repairType")
    @JsonProperty("repairType")
    private String repairType;

    @ApiModelProperty(value = "不良描述")
    @Field("repairFail")
    @JsonProperty("repairFail")
    private String repairFail;

    @ApiModelProperty(value = "申报不良工序编码")
    @Field("repairProcessCode")
    @JsonProperty("repairProcessCode")
    private String repairProcessCode;

    @ApiModelProperty(value = "工序名称")
    @Field("repairProcessName")
    @JsonProperty("repairProcessName")
    private String repairProcessName;

    @ApiModelProperty(value = "工站IP地址")
    @Field("repairStationIP")
    @JsonProperty("repairStationIP")
    private String repairStationIP;

    @ApiModelProperty(value = "工站名称")
    @Field("repairStationName")
    @JsonProperty("repairStationName")
    private String repairStationName;

    @ApiModelProperty(value = "操作员账号")
    @Field("repairOperatorId")
    @JsonProperty("repairOperatorId")
    private String repairOperatorId;

    @ApiModelProperty(value = "操作员姓名")
    @Field("repairOperatorName")
    @JsonProperty("repairOperatorName")
    private String repairOperatorName;

    @ApiModelProperty(value = "不良申报时间")
    @Field("repairTime")
    @JsonProperty("repairTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime repairTime;

    @ApiModelProperty(value = "重投工序编码")
    @Field("reinputProcessCode")
    @JsonProperty("reinputProcessCode")
    private String reinputProcessCode;

    @ApiModelProperty(value = "重投工序名称")
    @Field("reinputProcessName")
    @JsonProperty("reinputProcessName")
    private String reinputProcessName;
}
