package com.dinglite.mongodb.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品基础信息DTO
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProductInfoDTO对象", description = "产品基础信息")
public class ProductInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "MongoDB 文档唯一标识")
    @JsonProperty("_id")
    private String id;

    @ApiModelProperty(value = "集合名称")
    @JsonProperty("collection")
    private String collection;

    @ApiModelProperty(value = "产品序列号（业务主键）")
    @JsonProperty("sn")
    private String sn;

    @ApiModelProperty(value = "产品型号名称")
    @JsonProperty("modelName")
    private String modelName;

    @ApiModelProperty(value = "产品型号编码")
    @JsonProperty("modelCode")
    private String modelCode;

    @ApiModelProperty(value = "客户物料号")
    @JsonProperty("customerPN")
    private String customerPN;

    @ApiModelProperty(value = "客户订单号")
    @JsonProperty("customerOrder")
    private String customerOrder;

    @ApiModelProperty(value = "生产工单号")
    @JsonProperty("productionOrder")
    private String productionOrder;

    @ApiModelProperty(value = "生产车间名称")
    @JsonProperty("workshopName")
    private String workshopName;

    @ApiModelProperty(value = "产线名称")
    @JsonProperty("lineName")
    private String lineName;

    @ApiModelProperty(value = "产线编码")
    @JsonProperty("lineCode")
    private String lineCode;

    @ApiModelProperty(value = "产品状态")
    @JsonProperty("status")
    private String status;

    @ApiModelProperty(value = "是否直通品标识")
    @JsonProperty("isDirectProduct")
    private Boolean isDirectProduct;

    @ApiModelProperty(value = "投产时间")
    @JsonProperty("startTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "最后工序名称")
    @JsonProperty("lastProcess")
    private String lastProcess;

    @ApiModelProperty(value = "已完成工序列表")
    @JsonProperty("completedProcesses")
    private List<String> completedProcesses;

    @ApiModelProperty(value = "已完成工序名称列表（包含工序名称）")
    @JsonProperty("completedProcessNames")
    private List<String> completedProcessNames;

    @ApiModelProperty(value = "最后工站名称")
    @JsonProperty("lastStation")
    private String lastStation;

    @ApiModelProperty(value = "包装状态")
    @JsonProperty("packageStatus")
    private String packageStatus;

    @ApiModelProperty(value = "小包装编码")
    @JsonProperty("packageCode")
    private String packageCode;

    @ApiModelProperty(value = "内箱编码")
    @JsonProperty("innerBoxCode")
    private String innerBoxCode;

    @ApiModelProperty(value = "外箱编码")
    @JsonProperty("outerBoxCode")
    private String outerBoxCode;

    @ApiModelProperty(value = "栈板编码")
    @JsonProperty("palletCode")
    private String palletCode;

    @ApiModelProperty(value = "入库时间")
    @JsonProperty("inboundTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inboundTime;

    @ApiModelProperty(value = "出库时间")
    @JsonProperty("outboundTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime outboundTime;
}
