        /// <summary>
        /// 从MongoDB数据库查询编码对应的玻璃信息
        /// </summary>
        /// <param name="code">查询编码</param>
        private async Task GetGlassData(object code)
        {
            DataSet ds = null;
            ProductInfo productInfo = null;
            List<ProductProcess> productProcessList = null;
            List<ProductMaterial> productMaterialList = null;

            string searchCode = "";

            try
            {
                if (code != null)
                {
                    // 待查询玻璃编码
                    searchCode = code.ToString();

                    // 从MongoDB查询产品基本信息
                    productInfo = await mongo.GetProductInfoAsync(searchCode);

                    // 从MongoDB查询产品过站履历
                    productProcessList = await mongo.GetProductProcessListAsync(searchCode);

                    // 从MongoDB查询产品物料批次信息
                    productMaterialList = await mongo.GetProductMaterialListAsync(searchCode);

                }
            }
            catch (Exception ex)
            {
                LogHelper.Error(ex.Message, ex);
            }
            finally
            {
                ShowProductInfo(productInfo, productProcessList, productMaterialList);
            }
        }

        /// <summary>
        /// 跨线程展示玻璃信息的委托
        /// </summary>
        /// <param name="shareModel"></param>
        private delegate void ShowProductInfoCallback(ProductInfo productInfo, List<ProductProcess> productProcess, List<ProductMaterial> productMaterials);
        /// <summary>
        /// 展示玻璃信息
        /// </summary>
        /// <param name="code"></param>
        /// <param name="glassData"></param>
        private void ShowProductInfo(ProductInfo productInfo, List<ProductProcess> productProcess, List<ProductMaterial> productMaterials)
        {
            // InvokeRequired required compares the thread ID of the
            // calling thread to the thread ID of the creating thread.
            // If these threads are different, it returns true.
            if (this.InvokeRequired)
            {
                ShowProductInfoCallback d = new ShowProductInfoCallback(ShowProductInfo);
                this.Invoke(d, new object[] { productInfo, productProcess, productMaterials});
            }
            else
            {
                try
                {
                    //从MySQl查询工序列表
                    string sql = "SELECT operation_code, operation_name FROM `operation` WHERE flag='N' ORDER BY operation_code";
                    Dictionary<string,string> dicProcess = conn.ExecuteDataTable(sql, "MySql_product")
                        .Rows.Cast<DataRow>()
                        .ToDictionary(row => row["operation_code"].ToString(), row => row["operation_name"].ToString());

                    if (productInfo != null)
                    {
                        // 产品SN
                        txtProductSN.Text = productInfo.sn;
                        // 玻璃状态
                        txtStatus.Text = productInfo.status;
                        // 是否直通品
                        txtRepeat.Text = productInfo.isDirectProduct? "是" : "否";
                        // 成品型号
                        txtModel.Text = productInfo.modelName;
                        //存货编码
                        txtModelCode.Text = productInfo.modelCode;
                        // 生产工单
                        txtOrder.Text = productInfo.productionOrder;
                        //生产日期
                        txtStartTime.Text = productInfo.startTime.ToString("yyyy-MM-dd HH:mm:ss");
                        //产线名称
                        txtLineName.Text = productInfo.lineName;
                        // 产线编码
                        txtLineCode.Text = productInfo.lineCode;
                        //客户料号
                        txtCusCode.Text = productInfo.customerPN;
                        //客户订单号
                        txtCustomerOrder.Text = productInfo.customerOrder;
                        //当前工序
                        if(!string.IsNullOrEmpty(productInfo.lastProcess) && dicProcess !=null && dicProcess.ContainsKey(productInfo.lastProcess))
                        {
                            txtLastProcess.Text =string.Format("{0}({1})", productInfo.lastProcess, dicProcess[productInfo.lastProcess]);
                        }
                        else
                        {
                            txtLastProcess.Text = productInfo.lastProcess;
                        }
                        //当前工站
                        txtLastStation.Text = productInfo.lastStation;
                        //已完成工序
                        string completedProcesses = "";
                        foreach (var process in productInfo.completedProcesses)
                        {
                            if (dicProcess != null && dicProcess.ContainsKey(process))
                            {
                                completedProcesses += dicProcess[process] + ", ";
                            }
                            else
                            {
                                completedProcesses += process + ", ";
                            }
                        }
                        txtCompletedProcesses.Text = completedProcesses.TrimEnd(',', ' ');


                        //成品包装信息
                        txtSubJobCode.Text = productInfo.packageCode;
                        txtInnerJobCode.Text = productInfo.innerBoxCode;
                        txtOuterJobCode.Text = productInfo.outerBoxCode; 
                        txtPalletJobCode.Text = productInfo.palletCode;
                    }

                    // 产品过站履历
                    if (productProcess != null && productProcess.Count > 0)
                    {
                        // 清除列表视图
                        dgwProcess.Rows.Clear();
                        int idx = 1;
                        foreach (var process in productProcess)
                        {
                            dgwProcess.Rows.Add(
                                idx++,
                                process.lineName,
                                process.processName,
                                process.processTime.ToString("yyyy-MM-dd HH:mm:ss"),
                                process.stationIP,
                                process.operatorName,
                                process.result,
                                string.Join(", ", process.failDesc.ToArray()),
                                process.scanCode
                            );
                        }
                    }

                    if(productMaterials != null && productMaterials.Count > 0)
                    {
                        // 物料批次信息
                        DataTable dtMaterial = new DataTable();
                        dtMaterial.Columns.Add("material_Code");
                        dtMaterial.Columns.Add("material_Name");
                        dtMaterial.Columns.Add("material_Spec");
                        dtMaterial.Columns.Add("material_Vonder");
                        dtMaterial.Columns.Add("material_SCDate");
                        dtMaterial.Columns.Add("material_Batch");
                        dtMaterial.Columns.Add("QR_Code");
                        dtMaterial.Columns.Add("material_FeedingOP");
                        foreach (var material in productMaterials)
                        {
                            DataRow row = dtMaterial.NewRow();
                            row["material_Code"] = material.materialCode;
                            row["material_Name"] = material.materialName;
                            row["material_Spec"] = material.materialSpec;
                            row["material_Vonder"] = material.supplier;
                            row["material_SCDate"] = material.productDate.ToString("yyyy-MM-dd HH:mm:ss");
                            row["material_Batch"] = material.lotNo;
                            row["QR_Code"] = material.qrCode;
                            row["material_FeedingOP"] = material.operatorName;
                            dtMaterial.Rows.Add(row);
                        }
                        dgwMaterial.AutoGenerateColumns = false;
                        dgwMaterial.DataSource = dtMaterial;
                    }

                }//try
                catch (Exception ex)
                {
                    LogHelper.Error(ex.Message, ex);
                    //ExceptionResult("获取玻璃信息失败。" + ex.Message.ToString());
                }
                finally
                {
                    txtCode.Enabled = true;
                    txtCode.Focus();
                    txtCode.SelectAll();
                }

                //Dictionary<string, dynamic> dic = GetHBaseDataClass.Instance.GetAllData(txtCode.Text);
            }

        }




        /// <summary>
        /// 通过产品序列号（SN）异步查询产品基本信息
        /// </summary>
        /// <param name="sn">产品SN</param>
        /// <returns></returns>
        public async Task<ProductInfo> GetProductInfoAsync(string sn)
        {
            try
            {
                return await _collection_product_info.Find(p => p.sn == sn).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查询异常: {ex.Message}");
                LogHelper.Error("MongoDB查询产品基本信息异常：" + ex.Message, ex);
                return null;
            }
        }

        /// <summary>
        /// 通过产品序列号（SN）获取产品过站履历
        /// </summary>
        /// <param name="sn">产品SN</param>
        /// <returns></returns>
        public async Task<List<ProductProcess>> GetProductProcessListAsync(string sn)
        {
            try
            {
                // 构建查询过滤器（按SN精确匹配）
                var filter = Builders<ProductProcess>.Filter.Eq(p => p.meta.sn, sn);
                //ProductProcess process = await _collection_product_process.Find(p => p.meta.sn == sn).FirstOrDefaultAsync();
                return await _collection_product_process.Find(filter).ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查询异常: {ex.Message}");
                LogHelper.Error("MongoDB查询产品过站履历异常：" + ex.Message, ex);
                return null;
            }
        }


        /// <summary>
        /// 通过产品序列号（SN）获取产品过站履历
        /// </summary>
        /// <param name="sn">产品SN</param>
        /// <returns></returns>
        public async Task<List<ProductMaterial>> GetProductMaterialListAsync(string sn)
        {
            try
            {
                // 构建查询过滤器（按SN精确匹配）
                var filter = Builders<ProductMaterial>.Filter.Eq(p => p.sn, sn);
                //ProductProcess process = await _collection_product_process.Find(p => p.meta.sn == sn).FirstOrDefaultAsync();
                return await _collection_product_material.Find(filter).ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查询异常: {ex.Message}");
                LogHelper.Error("MongoDB查询产品物料追溯信息异常：" + ex.Message, ex);
                return null;
            }
        }
